# Tree Navigation and Contentful Management

This document explains the comprehensive tree navigation system that processes console quiz data and creates/manages Contentful entries with efficient caching.

## Overview

The system provides a complete solution for:
- **Navigating tree structures** from console JSON files
- **Creating/updating Contentful entries** for quiz views and result panels
- **Linking entries together** in hierarchical relationships
- **Setting defined values** from JSON data
- **Efficient caching** to avoid repeated API calls

## Key Components

### 1. QuizTreeManager
Main class that provides a unified interface for all operations.

### 2. TreeNavigator
Handles recursive tree traversal and entry creation.

### 3. EntryLinker
Manages linking entries together and updating field values.

### 4. EnvironmentManager
Provides cached access to Contentful environments.

## Quick Start

### CLI Usage

```bash
# Process all console files and create Contentful entries
npm run cli process-all

# Process a single console
npm run cli process-single "PlayStation 5 Pro"

# Complete workflow (process + link to main quiz)
npm run cli complete-workflow

# List available consoles
npm run cli list-consoles

# Validate quiz tree structure
npm run cli validate

# Show cache statistics
npm run cli cache-stats
```

### Programmatic Usage

```typescript
import { QuizTreeManager, QuizTreeUtils } from './quizTreeManager';

// Create manager instance
const manager = new QuizTreeManager('staging');

// Process all console files
await manager.processAllConsoles();

// Link console entries to main quiz
await manager.linkEntries(
  'mainQuizId',
  'productQuizView',
  ['console1Id', 'console2Id']
);

// Set field values
await manager.setFieldValues('entryId', {
  header: 'New header text',
  subtitle: 'New subtitle text'
});
```

## Data Structure

### Console JSON Format
```json
{
  "productQuizView": [
    {
      "selectorText": "PlayStation 5 Pro",
      "id": "4ksProductQuiz_playstation5pro",
      "title": "4K S / Product Quiz / PlayStation 5 Pro",
      "header": "What resolution is your source set to?",
      "subtitle": "In other words, what do you want to capture?",
      "productQuizView": [
        {
          "selectorText": "1080p",
          "id": "4ksProductQuiz_playstation5pro-1080p",
          "title": "4K S / Product Quiz / PlayStation 5 Pro / 1080p",
          "result": {
            "id": "result-id",
            "title": "Result Title",
            "calloutTitle": "You can capture",
            "headline": "Up to: 1080p60 SDR",
            "disclaimerText": "Additional information..."
          }
        }
      ]
    }
  ]
}
```

## Content Types

### productQuizView
- `title` (Text): Entry title
- `selectorText` (Text): Button/selector text
- `header` (Text): Question header
- `subtitle` (Text): Question subtitle
- `productQuizView` (References): Child quiz views

### organismHeadlineMedia (Result Panels)
- `title` (Text): Panel title
- `calloutTitle` (Text): Callout text
- `headline` (Text): Main headline
- `disclaimerText` (Rich Text): Disclaimer content
- `logoPlacement` (Text): Logo position
- `textAlignment` (Text): Text alignment

## Core Functions

### Tree Navigation

```typescript
// Process all console files
await manager.processAllConsoles();

// Process specific console
await manager.processSingleConsole('PlayStation 5 Pro');

// Get console tree structure
const tree = manager.getConsoleTree('PlayStation 5 Pro');
```

### Entry Management

```typescript
// Create or update entries with linking
await processTreeNode(node, 'staging');

// Link entries to parent
await manager.linkEntries(
  'parentId',
  'productQuizView',
  ['child1', 'child2'],
  false // Don't replace existing
);

// Update field values
await manager.setFieldValues('entryId', {
  header: 'New header',
  subtitle: 'New subtitle'
});
```

### Bulk Operations

```typescript
// Bulk update multiple entries
await manager.bulkSetFieldValues([
  {
    entryId: 'entry1',
    fieldValues: { header: 'Header 1' }
  },
  {
    entryId: 'entry2',
    fieldValues: { header: 'Header 2' }
  }
]);
```

### Validation

```typescript
// Validate linked entries
const validation = await manager.validateLinkedEntries(
  'parentId',
  'productQuizView'
);

console.log(`Valid: ${validation.valid.length}`);
console.log(`Invalid: ${validation.invalid.length}`);
```

## Workflow Examples

### Complete Setup Workflow

```typescript
import { QuizTreeUtils } from './quizTreeManager';

// Process all consoles and link to main quiz
await QuizTreeUtils.setupCompleteQuizTree('mainQuizEntryId');
```

### Selective Processing

```typescript
// Process only specific consoles
await QuizTreeUtils.processSpecificConsoles([
  'PlayStation 5 Pro',
  'Xbox Series X',
  'PC'
]);
```

### Configuration Updates

```typescript
// Update console configuration
await manager.updateConsoleConfiguration('PlayStation 5 Pro', {
  header: 'What resolution is your PlayStation 5 Pro set to?',
  subtitle: 'Check your console settings for this information.'
});
```

## Performance Features

### Environment Caching
- Environments are cached after first access
- Subsequent operations use cached environments
- Significant performance improvement for bulk operations

### Cache Management
```typescript
// Check cache status
const stats = manager.getCacheStats();
console.log(`Cached environments: ${stats.environmentCount}`);

// Clear cache if needed
manager.clearCache();
```

## Error Handling

The system includes comprehensive error handling:
- Graceful handling of missing entries
- Validation of linked references
- Detailed error logging with colors
- Continuation of processing even if individual entries fail

## CLI Commands Reference

| Command | Description | Example |
|---------|-------------|---------|
| `process-all` | Process all console files | `npm run cli process-all` |
| `process-single <console>` | Process single console | `npm run cli process-single "PC"` |
| `complete-workflow` | Full workflow | `npm run cli complete-workflow` |
| `list-consoles` | List available consoles | `npm run cli list-consoles` |
| `validate` | Validate tree structure | `npm run cli validate` |
| `cache-stats` | Show cache statistics | `npm run cli cache-stats` |
| `clear-cache` | Clear environment cache | `npm run cli clear-cache` |
| `link-consoles` | Link consoles to main quiz | `npm run cli link-consoles` |
| `update-console <console>` | Update console config | `npm run cli update-console "PC"` |

## Best Practices

1. **Use the CLI** for one-off operations and testing
2. **Use QuizTreeManager** for programmatic integration
3. **Validate entries** after bulk operations
4. **Monitor cache performance** for optimization
5. **Process consoles incrementally** for large datasets
6. **Backup data** before running bulk operations

## Troubleshooting

### Common Issues

1. **Missing console files**: Ensure JSON files exist in `output_consoles/`
2. **Invalid entry references**: Run validation to identify broken links
3. **API rate limits**: Use caching and process in smaller batches
4. **Memory issues**: Clear cache periodically for large operations

### Debug Information

```typescript
// Enable detailed logging
console.log('Available consoles:', manager.getAvailableConsoles());
console.log('Cache stats:', manager.getCacheStats());

// Validate specific entries
const validation = await manager.validateLinkedEntries('entryId', 'field');
```
