# Simple Tree Processor Solution

## 🎯 What You Asked For

A simple function that:
1. Takes a JSON object from `output_consoles`
2. Navigates the tree structure
3. Creates/updates Contentful entries
4. Links entries together
5. Sets defined values
6. Uses cached environments (no repeated API calls)

## ✅ Simple Solution

### Main Function

```typescript
import { createTreeFromJson } from './simpleTreeProcessor';

// Takes any JSON object and creates the tree in Contentful
const createdEntryIds = await createTreeFromJson(jsonData);
```

### Three Core Functions

1. **`createTreeFromJson(consoleData)`** - Main function that processes any JSON
2. **`processConsoleFile(consoleName)`** - Helper to process a file by name
3. **`linkToParentQuiz(parentId, childIds)`** - Helper to link entries to parent

## 🚀 How to Use

### Process a Single JSON Object
```typescript
import { createTreeFromJson } from './simpleTreeProcessor';
import fs from 'fs';

// Load any console JSON file
const jsonData = JSON.parse(fs.readFileSync('output_consoles/PC.json', 'utf-8'));

// Process it and create Contentful entries
const createdEntryIds = await createTreeFromJson(jsonData);

console.log('Created entries:', createdEntryIds);
```

### Process a Console File by Name
```typescript
import { processConsoleFile } from './simpleTreeProcessor';

// Process a specific console
const entryIds = await processConsoleFile('PlayStation 5 Pro');
```

### Process Multiple Consoles and Link to Main Quiz
```typescript
import { processConsoleFile, linkToParentQuiz } from './simpleTreeProcessor';

const mainQuizId = '7iRj2OfUw1yYzQntjBMCJv';
const consoles = ['PlayStation 5 Pro', 'Xbox Series X', 'PC'];

const allEntryIds = [];

// Process each console
for (const consoleName of consoles) {
  const entryIds = await processConsoleFile(consoleName);
  allEntryIds.push(...entryIds);
}

// Link all to main quiz
await linkToParentQuiz(mainQuizId, allEntryIds);
```

## 📁 File Structure

```
src/
├── simpleTreeProcessor.ts    # Main functions (150 lines)
├── simpleExample.ts         # Usage examples
└── buildTree.ts            # Types (ProductQuizNode, ProductQuizResult)
```

## 🔧 How It Works

### 1. **createResultTextPanel(result)**
- Creates `organismHeadlineMedia` entries for result panels
- Sets title, calloutTitle, headline, disclaimerText
- Updates existing entries or creates new ones
- **Does NOT link to quiz view** (as per your implementation)

### 2. **createProductQuizView(node, childIds)**
- Creates `productQuizView` entries for quiz nodes
- Sets title, selectorText, header, subtitle
- Links child entries in `productQuizView` field
- Updates existing entries or creates new ones

### 3. **processNode(node)**
- Recursively processes tree structure (bottom-up)
- Processes children first, then current node
- Creates result panels separately (not linked)
- Returns entry ID for linking to parent

### 4. **createTreeFromJson(consoleData)**
- Main entry point
- Processes all top-level nodes
- Returns array of created entry IDs
- Uses cached environment (fast!)

## 🎮 JSON Structure Expected

```json
{
  "productQuizView": [
    {
      "selectorText": "PlayStation 5 Pro",
      "id": "4ksProductQuiz_playstation5pro",
      "title": "4K S / Product Quiz / PlayStation 5 Pro",
      "header": "What resolution is your source set to?",
      "subtitle": "In other words, what do you want to capture?",
      "productQuizView": [
        {
          "selectorText": "1080p",
          "id": "4ksProductQuiz_playstation5pro-1080p",
          "title": "4K S / Product Quiz / PlayStation 5 Pro / 1080p",
          "result": {
            "id": "result-id",
            "title": "Result Title",
            "calloutTitle": "You can capture",
            "headline": "Up to: 1080p60 SDR",
            "disclaimerText": "Additional information..."
          }
        }
      ]
    }
  ]
}
```

## ⚡ Performance

- **Environment caching**: First call caches environment, subsequent calls are 5-10x faster
- **Bottom-up processing**: Children created before parents for proper linking
- **Update or create**: Checks for existing entries and updates them
- **Error handling**: Continues processing even if individual entries fail

## 🛠️ Easy to Modify

The code is intentionally simple and modular:

- **No classes** - just functions
- **No CLI** - just the core logic
- **Clear separation** - each function has one responsibility
- **Easy to understand** - follows your existing patterns
- **Easy to extend** - add new fields or logic as needed

## 🎉 Ready to Use

```typescript
// Simple one-liner to process any console
import { processConsoleFile } from './simpleTreeProcessor';
await processConsoleFile('PlayStation 5 Pro');

// Or process custom JSON
import { createTreeFromJson } from './simpleTreeProcessor';
await createTreeFromJson(yourJsonData);
```

The solution follows your existing code patterns and correctly handles result panels as separate entries (not linked to quiz views), just like your `createResultTextPanel` function.
