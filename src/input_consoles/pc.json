["PC-1080p-60-SDR_1080p60 SDR", "PC-1080p-60-HDR_1080p60 HDR", "PC-1080p-120-SDR_1080p120 SDR", "PC-1080p-120-HDR_1080p60 HDR or 1080p120 SDR", "PC-1080p-240-SDR_1080p240 SDR", "PC-1080p-240-HDR_1080p60 HDR or 1080p240 SDR", "PC-1440p-60-SDR_1440p60 SDR", "PC-1440p-60-HDR_1080p60 HDR or 1440p60 SDR", "PC-1440p-120-SDR_1440p120 SDR", "PC-1440p-120-HDR_1080p60 SDR or 1440p120 SDR", "PC-1440p-144-SDR_1440p144 SDR", "PC-1440p-144-HDR_1080p60 SDR or 1440p144", "PC-4K-60-SDR_4K60 SDR", "PC-4K-60-HDR_1080p60 HDR or 4K60 SDR", "PC-4K-120-SDR_4K S doesn’t support 4K120 capture. For full next-gen performance, consider 4K X. With HDMI 2.1, it delivers ultra-smooth passthrough of 4K120 with HDR10 and supports VRR (Variable Refresh Rate) for tear-free, low-latency gameplay. Capture options include 1440p60 HDR/4K30 HDR or 4K120 SDR, making it ideal for creators who want to game at peak fidelity without compromise.", "PC-4K-120-HDR_4K S doesn’t support 4K120 capture. For full next-gen performance, consider 4K X. With HDMI 2.1, it delivers ultra-smooth passthrough of 4K120 with HDR10 and supports VRR (Variable Refresh Rate) for tear-free, low-latency gameplay. Capture options include 1440p60 HDR/4K30 HDR or 4K120 SDR, making it ideal for creators who want to game at peak fidelity without compromise."]