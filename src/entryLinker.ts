import { environmentManager } from './environmentManager';
import { Entry } from 'contentful-management';
import chalk from 'chalk';

/**
 * Links multiple child entries to a parent entry in a specific field
 */
async function linkEntriesToEntry(
  parentEntryId: string,
  referenceFieldId: string,
  childEntryIds: string[],
  environmentId: string = 'staging',
  replaceExisting: boolean = false
): Promise<Entry> {
  console.log(chalk.blue(`🔗 Linking ${childEntryIds.length} entries to ${parentEntryId}.${referenceFieldId}`));
  
  try {
    const parentEntry = await environmentManager.getEntry(parentEntryId, environmentId);
    
    // Get current links (ensure array or initialize)
    const locale = 'en';
    const existingEntries: any[] = parentEntry.fields[referenceFieldId]?.[locale] || [];
    
    // Extract existing IDs for easy checking
    const existingIds = new Set(
      existingEntries.map((link: any) => link.sys.id)
    );
    
    let entriesToLink: any[];
    
    if (replaceExisting) {
      // Replace all existing links
      entriesToLink = childEntryIds.map((entryId) => ({
        sys: {
          type: 'Link',
          linkType: 'Entry',
          id: entryId,
        },
      }));
      
      console.log(chalk.yellow(`🔄 Replacing ${existingEntries.length} existing links with ${childEntryIds.length} new links`));
      
    } else {
      // Filter out IDs that are already linked
      const newEntryIds = childEntryIds.filter((id) => !existingIds.has(id));
      
      if (newEntryIds.length === 0) {
        console.log(chalk.blue(`ℹ️ No new entries to add to '${referenceFieldId}' for entry ${parentEntryId}.`));
        return parentEntry;
      }
      
      // Prepare new link objects
      const newLinks = newEntryIds.map((entryId) => ({
        sys: {
          type: 'Link',
          linkType: 'Entry',
          id: entryId,
        },
      }));
      
      // Add new entries to the existing array
      entriesToLink = [...existingEntries, ...newLinks];
      
      console.log(chalk.green(`➕ Adding ${newEntryIds.length} new entries to existing ${existingEntries.length} links`));
    }
    
    // Update the parent entry
    parentEntry.fields[referenceFieldId] = {
      [locale]: entriesToLink,
    };
    
    const updatedEntry = await parentEntry.update();
    
    console.log(chalk.green(`✅ Successfully linked entries to ${parentEntryId}.${referenceFieldId}`));
    return updatedEntry;
    
  } catch (error) {
    console.error(chalk.red(`❌ Error linking entries to ${parentEntryId}:`), error);
    throw error;
  }
}

/**
 * Removes specific child entries from a parent entry's reference field
 */
async function unlinkEntriesFromEntry(
  parentEntryId: string,
  referenceFieldId: string,
  childEntryIdsToRemove: string[],
  environmentId: string = 'staging'
): Promise<Entry> {
  console.log(chalk.blue(`🔓 Unlinking ${childEntryIdsToRemove.length} entries from ${parentEntryId}.${referenceFieldId}`));
  
  try {
    const parentEntry = await environmentManager.getEntry(parentEntryId, environmentId);
    
    // Get current links
    const locale = 'en';
    const existingEntries: any[] = parentEntry.fields[referenceFieldId]?.[locale] || [];
    
    // Filter out the entries to remove
    const idsToRemove = new Set(childEntryIdsToRemove);
    const remainingEntries = existingEntries.filter(
      (link: any) => !idsToRemove.has(link.sys.id)
    );
    
    const removedCount = existingEntries.length - remainingEntries.length;
    
    if (removedCount === 0) {
      console.log(chalk.blue(`ℹ️ No entries were removed from '${referenceFieldId}' for entry ${parentEntryId}.`));
      return parentEntry;
    }
    
    // Update the parent entry
    parentEntry.fields[referenceFieldId] = {
      [locale]: remainingEntries,
    };
    
    const updatedEntry = await parentEntry.update();
    
    console.log(chalk.green(`✅ Removed ${removedCount} entries from ${parentEntryId}.${referenceFieldId}`));
    return updatedEntry;
    
  } catch (error) {
    console.error(chalk.red(`❌ Error unlinking entries from ${parentEntryId}:`), error);
    throw error;
  }
}

/**
 * Sets or updates specific field values on an entry
 */
async function setEntryFieldValues(
  entryId: string,
  fieldValues: Record<string, any>,
  environmentId: string = 'staging',
  locale: string = 'en'
): Promise<Entry> {
  console.log(chalk.blue(`📝 Setting field values for entry ${entryId}`));
  
  try {
    const entry = await environmentManager.getEntry(entryId, environmentId);
    
    // Update each field
    for (const [fieldName, value] of Object.entries(fieldValues)) {
      if (!entry.fields[fieldName]) {
        entry.fields[fieldName] = {};
      }
      entry.fields[fieldName][locale] = value;
      
      console.log(chalk.cyan(`  📌 Set ${fieldName}: ${typeof value === 'object' ? JSON.stringify(value).substring(0, 50) + '...' : value}`));
    }
    
    const updatedEntry = await entry.update();
    
    console.log(chalk.green(`✅ Updated ${Object.keys(fieldValues).length} fields for entry ${entryId}`));
    return updatedEntry;
    
  } catch (error) {
    console.error(chalk.red(`❌ Error setting field values for ${entryId}:`), error);
    throw error;
  }
}

/**
 * Bulk operation to set field values on multiple entries
 */
async function bulkSetFieldValues(
  entryUpdates: Array<{
    entryId: string;
    fieldValues: Record<string, any>;
  }>,
  environmentId: string = 'staging',
  locale: string = 'en'
): Promise<Entry[]> {
  console.log(chalk.magenta(`🔄 Bulk updating ${entryUpdates.length} entries`));
  
  const updatedEntries: Entry[] = [];
  
  for (const { entryId, fieldValues } of entryUpdates) {
    try {
      const updatedEntry = await setEntryFieldValues(entryId, fieldValues, environmentId, locale);
      updatedEntries.push(updatedEntry);
    } catch (error) {
      console.error(chalk.red(`❌ Failed to update entry ${entryId}:`), error);
      // Continue with other entries
    }
  }
  
  console.log(chalk.green(`✅ Successfully updated ${updatedEntries.length}/${entryUpdates.length} entries`));
  return updatedEntries;
}

/**
 * Gets all linked entry IDs from a reference field
 */
async function getLinkedEntryIds(
  parentEntryId: string,
  referenceFieldId: string,
  environmentId: string = 'staging',
  locale: string = 'en'
): Promise<string[]> {
  try {
    const parentEntry = await environmentManager.getEntry(parentEntryId, environmentId);
    
    const linkedEntries: any[] = parentEntry.fields[referenceFieldId]?.[locale] || [];
    const linkedIds = linkedEntries.map((link: any) => link.sys.id);
    
    console.log(chalk.cyan(`📋 Found ${linkedIds.length} linked entries in ${parentEntryId}.${referenceFieldId}`));
    return linkedIds;
    
  } catch (error) {
    console.error(chalk.red(`❌ Error getting linked entries from ${parentEntryId}:`), error);
    throw error;
  }
}

/**
 * Validates that all referenced entries exist
 */
async function validateLinkedEntries(
  parentEntryId: string,
  referenceFieldId: string,
  environmentId: string = 'staging'
): Promise<{ valid: string[]; invalid: string[] }> {
  console.log(chalk.blue(`🔍 Validating linked entries for ${parentEntryId}.${referenceFieldId}`));
  
  const linkedIds = await getLinkedEntryIds(parentEntryId, referenceFieldId, environmentId);
  const valid: string[] = [];
  const invalid: string[] = [];
  
  for (const entryId of linkedIds) {
    try {
      await environmentManager.getEntry(entryId, environmentId);
      valid.push(entryId);
    } catch (error) {
      invalid.push(entryId);
    }
  }
  
  if (invalid.length > 0) {
    console.log(chalk.yellow(`⚠️ Found ${invalid.length} invalid references: ${invalid.join(', ')}`));
  } else {
    console.log(chalk.green(`✅ All ${valid.length} linked entries are valid`));
  }
  
  return { valid, invalid };
}

export {
  linkEntriesToEntry,
  unlinkEntriesFromEntry,
  setEntryFieldValues,
  bulkSetFieldValues,
  getLinkedEntryIds,
  validateLinkedEntries
};
