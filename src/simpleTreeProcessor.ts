import { environmentManager } from './environmentManager';
import { ProductQuizResult, ProductQuizNode } from './buildTree';
import { linkEntriesToEntry } from './quizCreator';
import chalk from 'chalk';

// Map selector text to Elgato media entry IDs
const selectorToElgatoMediaEntryIdMap: Record<string, string> = {
  '1080p': '33M773eoTZmYFrS3CWkSIb',
  '1440p': '6NB0XLXc2bLu6uw8AXA5RI',
  '4K': '5RMpxDrBGJ4kuJVBZgRAW',
  '60Hz': '4Qpw8iWbV3Nr6eJa9XMpkY',
  '120Hz': '3GnEttrOnhelON5feDokGe',
  '144Hz': '2nKyuyQZZtzYNTXVzqxUSJ',
  '240Hz': '6uHdPkvhABC5qiQ7ZbWlrS',
  'HDR': '1SoaRCdN2qanxB9I6xtxZY',
  'SDR': '3fAzrvLCW2ucr6XWNxFpc9'
};

/**
 * Creates a result text panel entry (organismHeadlineMedia)
 */
async function createResultTextPanel(result: ProductQuizResult): Promise<string> {
  const environment = await environmentManager.getEnvironment('staging');

  const fields: any = {
    title: { en: result.title },
  };

  if (result.calloutTitle) {
    fields.calloutTitle = { en: result.calloutTitle };
  }

  if (result.headline) {
    fields.headline = { en: result.headline };
  }

  if (result.disclaimerText) {
    fields.disclaimerText = {
      en: {
        data: {},
        content: [
          {
            nodeType: 'paragraph',
            data: {},
            content: [
              {
                nodeType: 'text',
                value: result.disclaimerText,
                marks: [],
                data: {},
              },
            ],
          },
        ],
        nodeType: 'document',
      },
    };
  }

  try {
    // Try to get existing entry first
    const existingEntry = await environmentManager.getEntry(result.id, 'staging');

    // Check if any fields are different and need updating
    let hasChanges = false;
    for (const [fieldName, fieldValue] of Object.entries(fields)) {
      const existingValue = existingEntry.fields[fieldName]?.en;
      const newValue = (fieldValue as any).en;

      if (JSON.stringify(existingValue) !== JSON.stringify(newValue)) {
        hasChanges = true;
        break;
      }
    }

    if (hasChanges) {
      // Update existing entry with new fields
      Object.assign(existingEntry.fields, fields);
      await existingEntry.update();
      console.log(chalk.yellow(`🔄 Updated result panel with changes: ${result.id}`));
    } else {
      console.log(chalk.blue(`ℹ️ Result panel unchanged: ${result.id}`));
    }

  } catch (error) {
    // Entry doesn't exist, create new one
    await environment.createEntryWithId('organismHeadlineMedia', result.id, { fields });
    console.log(chalk.green(`✅ Created result panel: ${result.id}`));
  }

  return result.id;
}

/**
 * Creates a product quiz view entry
 */
async function createProductQuizView(node: ProductQuizNode, childEntryIds: string[] = [], isResultNode: boolean = false): Promise<string> {
  const environment = await environmentManager.getEnvironment('staging');

  const fields: any = {
    title: { en: node.title },
    selectorText: { en: node.selectorText }
  };

  // Set variant to "sd-result" for result nodes
  if (isResultNode) {
    fields.variant = { en: 'sd-result' };
  }

  if (node.header) {
    fields.header = { en: node.header };
  }

  if (node.subtitle) {
    fields.subtitle = { en: node.subtitle };
  }

  if (childEntryIds.length > 0) {
    fields.productQuizView = {
      en: childEntryIds.map(id => ({
        sys: {
          type: 'Link',
          linkType: 'Entry',
          id: id
        }
      }))
    };
  }

  // Check if selectorText matches any media entry and link it
  const mediaEntryId = selectorToElgatoMediaEntryIdMap[node.selectorText];
  if (mediaEntryId) {
    fields.media = {
      en: {
        sys: {
          type: 'Link',
          linkType: 'Entry',
          id: mediaEntryId
        }
      }
    };
    console.log(chalk.cyan(`🎬 Linked media ${mediaEntryId} to ${node.selectorText}`));
  }

  try {
    // Try to get existing entry first
    const existingEntry = await environmentManager.getEntry(node.id, 'staging');

    // Check if any fields are different and need updating
    let hasChanges = false;
    for (const [fieldName, fieldValue] of Object.entries(fields)) {
      const existingValue = existingEntry.fields[fieldName]?.en;
      const newValue = (fieldValue as any).en;

      // Special handling for array fields (links)
      if (Array.isArray(newValue) && Array.isArray(existingValue)) {
        const existingIds = existingValue.map((item: any) => item.sys?.id).sort();
        const newIds = newValue.map((item: any) => item.sys?.id).sort();
        if (JSON.stringify(existingIds) !== JSON.stringify(newIds)) {
          hasChanges = true;
          break;
        }
      } else if (JSON.stringify(existingValue) !== JSON.stringify(newValue)) {
        hasChanges = true;
        break;
      }
    }

    if (hasChanges) {
      // Update existing entry with new fields
      Object.assign(existingEntry.fields, fields);
      await existingEntry.update();
      console.log(chalk.yellow(`🔄 Updated quiz view with changes: ${node.id}`));
    } else {
      console.log(chalk.blue(`ℹ️ Quiz view unchanged: ${node.id}`));
    }

  } catch (error) {
    // Entry doesn't exist, create new one
    await environment.createEntryWithId('productQuizView', node.id, { fields });
    console.log(chalk.green(`✅ Created quiz view: ${node.id}`));
  }

  return node.id;
}

/**
 * Recursively processes a tree node and its children
 */
async function processNode(node: ProductQuizNode): Promise<string> {
  const childEntryIds: string[] = [];

  // Process children first (bottom-up approach)
  if (node.productQuizView && node.productQuizView.length > 0) {
    for (const childNode of node.productQuizView) {
      const childEntryId = await processNode(childNode);
      childEntryIds.push(childEntryId);
    }
  }

  // Create result panel if it exists (but don't link it)
  if (node.result) {
    await createResultTextPanel(node.result);
  }

  // Check if this is a result node (has a result but no children)
  const isResultNode = node.result && (!node.productQuizView || node.productQuizView.length === 0);

  // Create the current quiz view node
  const entryId = await createProductQuizView(node, childEntryIds, isResultNode);

  return entryId;
}

/**
 * Main function: Takes a JSON object and creates the tree in Contentful
 * @param consoleData - JSON object with productQuizView array
 * @returns Array of created top-level entry IDs
 */
export async function createTreeFromJson(consoleData: { productQuizView: ProductQuizNode[] }): Promise<string[]> {
  console.log(chalk.blue(`🌳 Processing tree with ${consoleData.productQuizView.length} top-level nodes...`));
  
  const topLevelEntryIds: string[] = [];
  
  for (const topLevelNode of consoleData.productQuizView) {
    console.log(chalk.yellow(`📊 Processing: ${topLevelNode.title}`));
    const entryId = await processNode(topLevelNode);
    topLevelEntryIds.push(entryId);
  }
  
  console.log(chalk.green(`✅ Created ${topLevelEntryIds.length} top-level entries`));
  return topLevelEntryIds;
}

/**
 * Helper function: Process a single console file by name
 */
export async function processConsoleFile(consoleName: string): Promise<string[]> {
  const fs = require('fs');
  const path = require('path');
  
  const filePath = path.join(__dirname, '..', 'output_consoles', `${consoleName}.json`);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Console file not found: ${consoleName}.json`);
  }
  
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  const consoleData = JSON.parse(fileContent);
  
  return await createTreeFromJson(consoleData);
}

/**
 * Helper function: Link created entries to a parent quiz
 */
export async function linkToParentQuiz(parentQuizId: string, childEntryIds: string[]): Promise<void> {
  console.log(chalk.blue(`🔗 Linking ${childEntryIds.length} entries to parent quiz ${parentQuizId}`));
  
  await linkEntriesToEntry(
    parentQuizId,
    'productQuizView',
    childEntryIds
  );
  
  console.log(chalk.green(`✅ Successfully linked entries to parent quiz`));
}
