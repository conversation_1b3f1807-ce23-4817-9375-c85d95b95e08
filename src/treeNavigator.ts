import { environmentManager } from './environmentManager';
import { Entry } from 'contentful-management';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';

// Types for the tree structure
interface ProductQuizResult {
  id: string;
  title: string;
  calloutTitle?: string;
  headline?: string;
  disclaimerText?: string;
}

interface ProductQuizNode {
  selectorText: string;
  id: string;
  title: string;
  header?: string;
  subtitle?: string;
  productQuizView?: ProductQuizNode[];
  result?: ProductQuizResult;
}

interface ConsoleData {
  productQuizView: ProductQuizNode[];
}

// Content type IDs
const CONTENT_TYPES = {
  PRODUCT_QUIZ_VIEW: 'productQuizView',
  RESULT_PANEL: 'organismHeadlineMedia'
} as const;

/**
 * Creates or updates a result text panel entry
 */
async function createOrUpdateResultPanel(
  result: ProductQuizResult,
  environmentId: string = 'staging'
): Promise<Entry> {
  console.log(chalk.blue(`📝 Creating result panel: ${result.title}`));

  const fields: any = {
    title: { en: result.title },
    logoPlacement: { en: 'top' },
    textAlignment: { en: 'center' },
    animation: { en: false },
    hideLogoOnMobile: { en: false },
    calloutTag: { en: 'h4' },
    useCalloutAsBadge: { en: false }
  };

  // Add optional fields
  if (result.calloutTitle) {
    fields.calloutTitle = { en: result.calloutTitle };
  }

  if (result.headline) {
    fields.headline = { en: result.headline };
  }

  if (result.disclaimerText) {
    fields.disclaimerText = {
      en: {
        nodeType: 'document',
        data: {},
        content: [
          {
            nodeType: 'paragraph',
            data: {},
            content: [
              {
                nodeType: 'text',
                value: result.disclaimerText,
                marks: [],
                data: {}
              }
            ]
          }
        ]
      }
    };
  }

  try {
    // Try to get existing entry first
    const existingEntry = await environmentManager.getEntry(result.id, environmentId);
    
    // Update existing entry
    Object.assign(existingEntry.fields, fields);
    const updatedEntry = await existingEntry.update();
    console.log(chalk.green(`✅ Updated result panel: ${result.id}`));
    return updatedEntry;
    
  } catch (error) {
    // Entry doesn't exist, create new one
    const newEntry = await environmentManager.createEntryWithId(
      CONTENT_TYPES.RESULT_PANEL,
      result.id,
      fields,
      environmentId
    );
    console.log(chalk.green(`✅ Created result panel: ${result.id}`));
    return newEntry;
  }
}

/**
 * Creates or updates a product quiz view entry
 */
async function createOrUpdateQuizView(
  node: ProductQuizNode,
  childEntryIds: string[] = [],
  environmentId: string = 'staging'
): Promise<Entry> {
  console.log(chalk.blue(`🎯 Creating quiz view: ${node.title}`));

  const fields: any = {
    title: { en: node.title },
    selectorText: { en: node.selectorText }
  };

  // Add optional fields
  if (node.header) {
    fields.header = { en: node.header };
  }

  if (node.subtitle) {
    fields.subtitle = { en: node.subtitle };
  }

  // Add child links if any
  if (childEntryIds.length > 0) {
    fields.productQuizView = {
      en: childEntryIds.map(id => ({
        sys: {
          type: 'Link',
          linkType: 'Entry',
          id: id
        }
      }))
    };
  }

  try {
    // Try to get existing entry first
    const existingEntry = await environmentManager.getEntry(node.id, environmentId);
    
    // Update existing entry
    Object.assign(existingEntry.fields, fields);
    const updatedEntry = await existingEntry.update();
    console.log(chalk.green(`✅ Updated quiz view: ${node.id}`));
    return updatedEntry;
    
  } catch (error) {
    // Entry doesn't exist, create new one
    const newEntry = await environmentManager.createEntryWithId(
      CONTENT_TYPES.PRODUCT_QUIZ_VIEW,
      node.id,
      fields,
      environmentId
    );
    console.log(chalk.green(`✅ Created quiz view: ${node.id}`));
    return newEntry;
  }
}

/**
 * Recursively processes a tree node and its children
 */
async function processTreeNode(
  node: ProductQuizNode, 
  environmentId: string = 'staging'
): Promise<string> {
  const childEntryIds: string[] = [];
  
  // Process children first (bottom-up approach)
  if (node.productQuizView && node.productQuizView.length > 0) {
    for (const childNode of node.productQuizView) {
      const childEntryId = await processTreeNode(childNode, environmentId);
      childEntryIds.push(childEntryId);
    }
  }
  
  // Process result panel if it exists
  if (node.result) {
    await createOrUpdateResultPanel(node.result, environmentId);
    // Note: Result panels are not linked as children in this structure
  }
  
  // Create or update the current node
  await createOrUpdateQuizView(node, childEntryIds, environmentId);
  
  return node.id;
}

/**
 * Processes all console files and creates the complete tree structure
 */
async function processAllConsoles(environmentId: string = 'staging'): Promise<void> {
  console.log(chalk.magenta('🚀 Starting tree navigation and Contentful entry creation...'));
  
  const consolesDir = path.join(__dirname, '..', 'output_consoles');
  
  if (!fs.existsSync(consolesDir)) {
    throw new Error(`Console directory not found: ${consolesDir}`);
  }
  
  const consoleFiles = fs.readdirSync(consolesDir).filter(file => file.endsWith('.json'));
  
  console.log(chalk.cyan(`📁 Found ${consoleFiles.length} console files to process`));
  
  for (const fileName of consoleFiles) {
    const filePath = path.join(consolesDir, fileName);
    const consoleName = path.basename(fileName, '.json');
    
    console.log(chalk.yellow(`\n🎮 Processing console: ${consoleName}`));
    
    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      const consoleData: ConsoleData = JSON.parse(fileContent);
      
      // Process each top-level node in the console
      for (const topLevelNode of consoleData.productQuizView) {
        console.log(chalk.blue(`📊 Processing tree for: ${topLevelNode.title}`));
        await processTreeNode(topLevelNode, environmentId);
      }
      
      console.log(chalk.green(`✅ Completed processing: ${consoleName}`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Error processing ${consoleName}:`), error);
    }
  }
  
  console.log(chalk.magenta('\n🎉 Tree navigation and entry creation completed!'));
}

/**
 * Processes a single console file
 */
async function processSingleConsole(
  consoleName: string, 
  environmentId: string = 'staging'
): Promise<void> {
  console.log(chalk.magenta(`🚀 Processing single console: ${consoleName}`));
  
  const filePath = path.join(__dirname, '..', 'output_consoles', `${consoleName}.json`);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Console file not found: ${filePath}`);
  }
  
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const consoleData: ConsoleData = JSON.parse(fileContent);
    
    // Process each top-level node in the console
    for (const topLevelNode of consoleData.productQuizView) {
      console.log(chalk.blue(`📊 Processing tree for: ${topLevelNode.title}`));
      await processTreeNode(topLevelNode, environmentId);
    }
    
    console.log(chalk.green(`✅ Completed processing: ${consoleName}`));
    
  } catch (error) {
    console.error(chalk.red(`❌ Error processing ${consoleName}:`), error);
    throw error;
  }
}

export {
  processAllConsoles,
  processSingleConsole,
  processTreeNode,
  createOrUpdateQuizView,
  createOrUpdateResultPanel,
  ProductQuizNode,
  ProductQuizResult,
  ConsoleData
};
