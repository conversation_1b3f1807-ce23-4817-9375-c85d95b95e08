import { createTreeFromJson, processConsoleFile, linkToParentQuiz } from './simpleTreeProcessor';
import fs from 'fs';
import path from 'path';

/**
 * Example 1: Process a single console JSON object
 */
async function example1_ProcessJsonObject() {
  console.log('=== Example 1: Process JSON Object ===');
  
  // Load a console JSON file
  const filePath = path.join(__dirname, '..', 'output_consoles', 'PlayStation 5 Pro.json');
  const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  
  // Process the JSON and create Contentful entries
  const createdEntryIds = await createTreeFromJson(jsonData);
  
  console.log('Created entry IDs:', createdEntryIds);
}

/**
 * Example 2: Process a console file by name
 */
async function example2_ProcessConsoleFile() {
  console.log('=== Example 2: Process Console File ===');
  
  // Process a console file by name
  const createdEntryIds = await processConsoleFile('PC');
  
  console.log('Created entry IDs:', createdEntryIds);
}

/**
 * Example 3: Process multiple consoles and link to main quiz
 */
async function example3_ProcessMultipleAndLink() {
  console.log('=== Example 3: Process Multiple Consoles and Link ===');
  
  const mainQuizId = '7iRj2OfUw1yYzQntjBMCJv'; // Your main quiz entry ID
  const consolesToProcess = ['PlayStation 5 Pro', 'Xbox Series X', 'PC'];
  
  const allCreatedIds: string[] = [];
  
  // Process each console
  for (const consoleName of consolesToProcess) {
    console.log(`\nProcessing ${consoleName}...`);
    const entryIds = await processConsoleFile(consoleName);
    allCreatedIds.push(...entryIds);
  }
  
  // Link all created entries to the main quiz
  await linkToParentQuiz(mainQuizId, allCreatedIds);
  
  console.log(`\nTotal entries created and linked: ${allCreatedIds.length}`);
}

/**
 * Example 4: Process all console files
 */
async function example4_ProcessAllConsoles() {
  console.log('=== Example 4: Process All Console Files ===');
  
  const consolesDir = path.join(__dirname, '..', 'output_consoles');
  const consoleFiles = fs.readdirSync(consolesDir).filter(file => file.endsWith('.json'));
  
  const allCreatedIds: string[] = [];
  
  for (const fileName of consoleFiles) {
    const consoleName = path.basename(fileName, '.json');
    console.log(`\nProcessing ${consoleName}...`);
    
    try {
      const entryIds = await processConsoleFile(consoleName);
      allCreatedIds.push(...entryIds);
    } catch (error) {
      console.error(`Error processing ${consoleName}:`, error);
    }
  }
  
  console.log(`\nTotal entries created: ${allCreatedIds.length}`);
  return allCreatedIds;
}

/**
 * Example 5: Custom JSON processing with media linking
 */
async function example5_CustomJson() {
  console.log('=== Example 5: Custom JSON with Media Linking ===');

  // JSON structure that will trigger media linking
  const customJson = {
    productQuizView: [
      {
        selectorText: "Test Console",
        id: "test_console",
        title: "Test Console",
        header: "What resolution do you want?",
        subtitle: "Choose your preferred resolution",
        productQuizView: [
          {
            selectorText: "1080p", // This will link to media entry 33M773eoTZmYFrS3CWkSIb
            id: "test_console_1080p",
            title: "Test Console / 1080p",
            productQuizView: [
              {
                selectorText: "60Hz", // This will link to media entry 4Qpw8iWbV3Nr6eJa9XMpkY
                id: "test_console_1080p_60hz",
                title: "Test Console / 1080p / 60Hz",
                productQuizView: [
                  {
                    selectorText: "HDR", // This will link to media entry 1SoaRCdN2qanxB9I6xtxZY
                    id: "test_console_1080p_60hz_hdr",
                    title: "Test Console / 1080p / 60Hz / HDR",
                    result: {
                      id: "test_console_result",
                      title: "Test Console Result",
                      calloutTitle: "You can capture",
                      headline: "Up to: 1080p60 HDR",
                      disclaimerText: "This demonstrates media linking functionality."
                    }
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  };

  const createdEntryIds = await createTreeFromJson(customJson);
  console.log('Created custom entries with media links:', createdEntryIds);
}

// Run examples (uncomment the ones you want to test)
async function runExamples() {
  try {
    // await example1_ProcessJsonObject();
    // await example2_ProcessConsoleFile();
    // await example3_ProcessMultipleAndLink();
    // await example4_ProcessAllConsoles();
    // await example5_CustomJson();
    
    console.log('\n✅ Examples completed!');
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Export for use in other files
export {
  example1_ProcessJsonObject,
  example2_ProcessConsoleFile,
  example3_ProcessMultipleAndLink,
  example4_ProcessAllConsoles,
  example5_CustomJson
};

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}
