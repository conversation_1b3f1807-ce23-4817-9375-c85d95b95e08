import { processAllConsoles, processSingleConsole, ProductQuizNode } from './treeNavigator';
import { linkEntriesToEntry, setEntryFieldValues, bulkSetFieldValues, validateLinkedEntries } from './entryLinker';
import { environmentManager } from './environmentManager';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';

/**
 * Main QuizTreeManager class that provides a unified interface for:
 * - Navigating tree structures
 * - Creating/updating Contentful entries
 * - Linking entries together
 * - Setting defined values
 */
export class QuizTreeManager {
  private environmentId: string;

  constructor(environmentId: string = 'staging') {
    this.environmentId = environmentId;
  }

  /**
   * Process all console files and create the complete tree structure in Contentful
   */
  async processAllConsoles(): Promise<void> {
    console.log(chalk.magenta('🚀 Processing all console files...'));
    await processAllConsoles(this.environmentId);
  }

  /**
   * Process a single console file
   */
  async processSingleConsole(consoleName: string): Promise<void> {
    console.log(chalk.magenta(`🚀 Processing console: ${consoleName}`));
    await processSingleConsole(consoleName, this.environmentId);
  }

  /**
   * Link multiple child entries to a parent entry
   */
  async linkEntries(
    parentEntryId: string,
    referenceFieldId: string,
    childEntryIds: string[],
    replaceExisting: boolean = false
  ): Promise<void> {
    await linkEntriesToEntry(
      parentEntryId,
      referenceFieldId,
      childEntryIds,
      this.environmentId,
      replaceExisting
    );
  }

  /**
   * Set field values on a single entry
   */
  async setFieldValues(
    entryId: string,
    fieldValues: Record<string, any>
  ): Promise<void> {
    await setEntryFieldValues(entryId, fieldValues, this.environmentId);
  }

  /**
   * Set field values on multiple entries
   */
  async bulkSetFieldValues(
    entryUpdates: Array<{
      entryId: string;
      fieldValues: Record<string, any>;
    }>
  ): Promise<void> {
    await bulkSetFieldValues(entryUpdates, this.environmentId);
  }

  /**
   * Validate all linked entries in a reference field
   */
  async validateLinkedEntries(
    parentEntryId: string,
    referenceFieldId: string
  ): Promise<{ valid: string[]; invalid: string[] }> {
    return await validateLinkedEntries(parentEntryId, referenceFieldId, this.environmentId);
  }

  /**
   * Get all available console names from the output_consoles directory
   */
  getAvailableConsoles(): string[] {
    const consolesDir = path.join(__dirname, '..', 'output_consoles');
    
    if (!fs.existsSync(consolesDir)) {
      console.warn(chalk.yellow('⚠️ Console directory not found'));
      return [];
    }
    
    return fs.readdirSync(consolesDir)
      .filter(file => file.endsWith('.json'))
      .map(file => path.basename(file, '.json'));
  }

  /**
   * Get the tree structure for a specific console
   */
  getConsoleTree(consoleName: string): ProductQuizNode[] {
    const filePath = path.join(__dirname, '..', 'output_consoles', `${consoleName}.json`);
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`Console file not found: ${consoleName}.json`);
    }
    
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const consoleData = JSON.parse(fileContent);
    
    return consoleData.productQuizView;
  }

  /**
   * Complete workflow: Process consoles and link to main quiz
   */
  async completeWorkflow(mainQuizEntryId: string): Promise<void> {
    console.log(chalk.magenta('🎯 Starting complete quiz tree workflow...'));
    
    try {
      // Step 1: Process all console files
      console.log(chalk.blue('Step 1: Processing all console files...'));
      await this.processAllConsoles();
      
      // Step 2: Get available consoles and their top-level entry IDs
      console.log(chalk.blue('Step 2: Collecting console entry IDs...'));
      const consoles = this.getAvailableConsoles();
      const consoleEntryIds: string[] = [];
      
      for (const consoleName of consoles) {
        try {
          const tree = this.getConsoleTree(consoleName);
          if (tree.length > 0) {
            consoleEntryIds.push(tree[0].id); // Get the top-level entry ID
          }
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Could not get tree for ${consoleName}`));
        }
      }
      
      console.log(chalk.cyan(`Found ${consoleEntryIds.length} console entries to link`));
      
      // Step 3: Link console entries to main quiz
      console.log(chalk.blue('Step 3: Linking console entries to main quiz...'));
      await this.linkEntries(
        mainQuizEntryId,
        'productQuizView',
        consoleEntryIds,
        true // Replace existing links
      );
      
      // Step 4: Validate the structure
      console.log(chalk.blue('Step 4: Validating the complete structure...'));
      const validation = await this.validateLinkedEntries(
        mainQuizEntryId,
        'productQuizView'
      );
      
      console.log(chalk.green('✅ Complete workflow finished!'));
      console.log(chalk.cyan(`  - Valid console links: ${validation.valid.length}`));
      console.log(chalk.cyan(`  - Invalid console links: ${validation.invalid.length}`));
      
      if (validation.invalid.length > 0) {
        console.log(chalk.yellow('⚠️ Invalid entry IDs:'), validation.invalid);
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Error in complete workflow:'), error);
      throw error;
    }
  }

  /**
   * Update specific console configurations
   */
  async updateConsoleConfiguration(
    consoleName: string,
    updates: Record<string, any>
  ): Promise<void> {
    console.log(chalk.blue(`🔧 Updating configuration for ${consoleName}`));
    
    try {
      const tree = this.getConsoleTree(consoleName);
      
      if (tree.length === 0) {
        throw new Error(`No tree found for console: ${consoleName}`);
      }
      
      const topLevelEntry = tree[0];
      await this.setFieldValues(topLevelEntry.id, updates);
      
      console.log(chalk.green(`✅ Updated configuration for ${consoleName}`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Error updating ${consoleName}:`), error);
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    cachedEnvironments: string[];
    environmentCount: number;
  } {
    return {
      cachedEnvironments: environmentManager.getCachedEnvironmentIds(),
      environmentCount: environmentManager.getCachedEnvironmentIds().length
    };
  }

  /**
   * Clear environment cache
   */
  clearCache(): void {
    environmentManager.clearCache();
    console.log(chalk.yellow('🧹 Environment cache cleared'));
  }
}

/**
 * Factory function to create a QuizTreeManager instance
 */
export function createQuizTreeManager(environmentId: string = 'staging'): QuizTreeManager {
  return new QuizTreeManager(environmentId);
}

/**
 * Quick utility functions for common operations
 */
export const QuizTreeUtils = {
  /**
   * Process all consoles and link to main quiz in one command
   */
  async setupCompleteQuizTree(
    mainQuizEntryId: string,
    environmentId: string = 'staging'
  ): Promise<void> {
    const manager = new QuizTreeManager(environmentId);
    await manager.completeWorkflow(mainQuizEntryId);
  },

  /**
   * Process only specific consoles
   */
  async processSpecificConsoles(
    consoleNames: string[],
    environmentId: string = 'staging'
  ): Promise<void> {
    const manager = new QuizTreeManager(environmentId);
    
    for (const consoleName of consoleNames) {
      await manager.processSingleConsole(consoleName);
    }
  },

  /**
   * Validate entire quiz tree structure
   */
  async validateQuizTree(
    mainQuizEntryId: string,
    environmentId: string = 'staging'
  ): Promise<{ valid: string[]; invalid: string[] }> {
    const manager = new QuizTreeManager(environmentId);
    return await manager.validateLinkedEntries(mainQuizEntryId, 'productQuizView');
  }
};
