#!/usr/bin/env node

import { QuizTreeManager, QuizTreeUtils } from './quizTreeManager';
import chalk from 'chalk';

// Main quiz entry ID (update this to match your actual main quiz entry)
const MAIN_QUIZ_ENTRY_ID = '7iRj2OfUw1yYzQntjBMCJv';

/**
 * CLI interface for the Quiz Tree Manager
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    showHelp();
    return;
  }

  const manager = new QuizTreeManager('staging');

  try {
    switch (command) {
      case 'process-all':
        await processAllConsoles(manager);
        break;

      case 'process-single':
        const consoleName = args[1];
        if (!consoleName) {
          console.error(chalk.red('❌ Console name required. Usage: npm run cli process-single "PlayStation 5 Pro"'));
          return;
        }
        await processSingleConsole(manager, consoleName);
        break;

      case 'complete-workflow':
        await completeWorkflow(manager);
        break;

      case 'list-consoles':
        list<PERSON><PERSON><PERSON><PERSON>(manager);
        break;

      case 'validate':
        await validate<PERSON><PERSON><PERSON><PERSON>(manager);
        break;

      case 'cache-stats':
        showCache<PERSON>tats(manager);
        break;

      case 'clear-cache':
        manager.clearCache();
        break;

      case 'link-consoles':
        await linkConsolesToMainQuiz(manager);
        break;

      case 'update-console':
        const consoleToUpdate = args[1];
        if (!consoleToUpdate) {
          console.error(chalk.red('❌ Console name required. Usage: npm run cli update-console "PlayStation 5 Pro"'));
          return;
        }
        await updateConsoleConfig(manager, consoleToUpdate);
        break;

      default:
        console.error(chalk.red(`❌ Unknown command: ${command}`));
        showHelp();
    }
  } catch (error) {
    console.error(chalk.red('❌ Error:'), error);
    process.exit(1);
  }
}

async function processAllConsoles(manager: QuizTreeManager) {
  console.log(chalk.magenta('🚀 Processing all console files...'));
  await manager.processAllConsoles();
  console.log(chalk.green('✅ All console files processed successfully!'));
}

async function processSingleConsole(manager: QuizTreeManager, consoleName: string) {
  console.log(chalk.magenta(`🚀 Processing console: ${consoleName}`));
  await manager.processSingleConsole(consoleName);
  console.log(chalk.green(`✅ Console ${consoleName} processed successfully!`));
}

async function completeWorkflow(manager: QuizTreeManager) {
  console.log(chalk.magenta('🎯 Starting complete workflow...'));
  await manager.completeWorkflow(MAIN_QUIZ_ENTRY_ID);
  console.log(chalk.green('✅ Complete workflow finished!'));
}

function listConsoles(manager: QuizTreeManager) {
  const consoles = manager.getAvailableConsoles();
  console.log(chalk.cyan(`📋 Available consoles (${consoles.length}):`));
  consoles.forEach((consoleName, index) => {
    console.log(chalk.white(`  ${index + 1}. ${consoleName}`));
  });
}

async function validateQuizTree(manager: QuizTreeManager) {
  console.log(chalk.blue('🔍 Validating quiz tree structure...'));
  const validation = await manager.validateLinkedEntries(MAIN_QUIZ_ENTRY_ID, 'productQuizView');
  
  console.log(chalk.cyan(`Valid entries: ${validation.valid.length}`));
  console.log(chalk.cyan(`Invalid entries: ${validation.invalid.length}`));
  
  if (validation.invalid.length > 0) {
    console.log(chalk.yellow('⚠️ Invalid entry IDs:'));
    validation.invalid.forEach(id => console.log(chalk.red(`  - ${id}`)));
  } else {
    console.log(chalk.green('✅ All linked entries are valid!'));
  }
}

function showCacheStats(manager: QuizTreeManager) {
  const stats = manager.getCacheStats();
  console.log(chalk.cyan('📊 Cache Statistics:'));
  console.log(chalk.white(`  Cached environments: ${stats.environmentCount}`));
  if (stats.cachedEnvironments.length > 0) {
    console.log(chalk.white(`  Environment IDs: ${stats.cachedEnvironments.join(', ')}`));
  }
}

async function linkConsolesToMainQuiz(manager: QuizTreeManager) {
  console.log(chalk.blue('🔗 Linking console entries to main quiz...'));
  
  const consoles = manager.getAvailableConsoles();
  const consoleEntryIds: string[] = [];
  
  for (const consoleName of consoles) {
    try {
      const tree = manager.getConsoleTree(consoleName);
      if (tree.length > 0) {
        consoleEntryIds.push(tree[0].id);
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️ Could not get tree for ${consoleName}`));
    }
  }
  
  await manager.linkEntries(
    MAIN_QUIZ_ENTRY_ID,
    'productQuizView',
    consoleEntryIds,
    true // Replace existing
  );
  
  console.log(chalk.green(`✅ Linked ${consoleEntryIds.length} console entries to main quiz`));
}

async function updateConsoleConfig(manager: QuizTreeManager, consoleName: string) {
  console.log(chalk.blue(`🔧 Updating configuration for ${consoleName}...`));
  
  // Example configuration updates
  const updates = {
    header: `What resolution is your ${consoleName} set to?`,
    subtitle: 'Check your device\'s video output settings to find this information.'
  };
  
  await manager.updateConsoleConfiguration(consoleName, updates);
  console.log(chalk.green(`✅ Configuration updated for ${consoleName}`));
}

function showHelp() {
  console.log(chalk.magenta('🎮 Quiz Tree Manager CLI'));
  console.log(chalk.white('\nAvailable commands:'));
  console.log(chalk.cyan('  process-all') + chalk.white('              - Process all console files and create Contentful entries'));
  console.log(chalk.cyan('  process-single <console>') + chalk.white('   - Process a single console file'));
  console.log(chalk.cyan('  complete-workflow') + chalk.white('         - Run the complete workflow (process + link)'));
  console.log(chalk.cyan('  list-consoles') + chalk.white('             - List all available console files'));
  console.log(chalk.cyan('  validate') + chalk.white('                  - Validate the quiz tree structure'));
  console.log(chalk.cyan('  cache-stats') + chalk.white('               - Show environment cache statistics'));
  console.log(chalk.cyan('  clear-cache') + chalk.white('               - Clear the environment cache'));
  console.log(chalk.cyan('  link-consoles') + chalk.white('             - Link console entries to main quiz'));
  console.log(chalk.cyan('  update-console <console>') + chalk.white('  - Update configuration for a specific console'));
  
  console.log(chalk.white('\nExamples:'));
  console.log(chalk.gray('  npm run cli process-all'));
  console.log(chalk.gray('  npm run cli process-single "PlayStation 5 Pro"'));
  console.log(chalk.gray('  npm run cli complete-workflow'));
  console.log(chalk.gray('  npm run cli validate'));
}

// Run the CLI
if (require.main === module) {
  main().catch(console.error);
}

export { main as runCLI };
