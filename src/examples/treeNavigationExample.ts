import { processAllConsoles, processSingleConsole } from '../treeNavigator';
import { linkEntriesToEntry, setEntryFieldValues, validateLinkedEntries } from '../entryLinker';
import { environmentManager } from '../environmentManager';
import chalk from 'chalk';

/**
 * Example 1: Process all console files and create the complete tree structure
 */
async function exampleProcessAllConsoles() {
  console.log(chalk.magenta('🎯 Example 1: Processing all console files'));
  
  try {
    await processAllConsoles('staging');
    console.log(chalk.green('✅ Successfully processed all console files'));
  } catch (error) {
    console.error(chalk.red('❌ Error processing console files:'), error);
  }
}

/**
 * Example 2: Process a single console file
 */
async function exampleProcessSingleConsole() {
  console.log(chalk.magenta('🎯 Example 2: Processing single console file'));
  
  try {
    await processSingleConsole('PlayStation 5 Pro', 'staging');
    console.log(chalk.green('✅ Successfully processed PlayStation 5 Pro'));
  } catch (error) {
    console.error(chalk.red('❌ Error processing single console:'), error);
  }
}

/**
 * Example 3: Link existing entries to a parent quiz
 */
async function exampleLinkEntriesToParent() {
  console.log(chalk.magenta('🎯 Example 3: Linking entries to parent quiz'));
  
  try {
    // Example: Link console entries to the main product quiz
    const mainQuizId = '7iRj2OfUw1yYzQntjBMCJv'; // Main product quiz entry
    const consoleEntryIds = [
      '4ksProductQuiz_playstation5pro',
      '4ksProductQuiz_pc',
      '4ksProductQuiz_xboxseriesx'
    ];
    
    await linkEntriesToEntry(
      mainQuizId,
      'productQuizView',
      consoleEntryIds,
      'staging',
      false // Don't replace existing links
    );
    
    console.log(chalk.green('✅ Successfully linked console entries to main quiz'));
  } catch (error) {
    console.error(chalk.red('❌ Error linking entries:'), error);
  }
}

/**
 * Example 4: Update field values on multiple entries
 */
async function exampleUpdateFieldValues() {
  console.log(chalk.magenta('🎯 Example 4: Updating field values'));
  
  try {
    // Update header and subtitle for a specific entry
    await setEntryFieldValues(
      '4ksProductQuiz_playstation5pro',
      {
        header: 'What resolution is your PlayStation 5 Pro set to?',
        subtitle: 'Check your console\'s video output settings to find this information.'
      },
      'staging'
    );
    
    console.log(chalk.green('✅ Successfully updated field values'));
  } catch (error) {
    console.error(chalk.red('❌ Error updating field values:'), error);
  }
}

/**
 * Example 5: Validate all linked entries in a quiz tree
 */
async function exampleValidateLinkedEntries() {
  console.log(chalk.magenta('🎯 Example 5: Validating linked entries'));
  
  try {
    const mainQuizId = '7iRj2OfUw1yYzQntjBMCJv';
    const validation = await validateLinkedEntries(
      mainQuizId,
      'productQuizView',
      'staging'
    );
    
    console.log(chalk.cyan(`Valid entries: ${validation.valid.length}`));
    console.log(chalk.cyan(`Invalid entries: ${validation.invalid.length}`));
    
    if (validation.invalid.length > 0) {
      console.log(chalk.yellow('Invalid entry IDs:'), validation.invalid);
    }
    
  } catch (error) {
    console.error(chalk.red('❌ Error validating entries:'), error);
  }
}

/**
 * Example 6: Complete workflow - Process consoles and link to main quiz
 */
async function exampleCompleteWorkflow() {
  console.log(chalk.magenta('🎯 Example 6: Complete workflow'));
  
  try {
    // Step 1: Process all console files
    console.log(chalk.blue('Step 1: Processing all console files...'));
    await processAllConsoles('staging');
    
    // Step 2: Get the main quiz entry
    console.log(chalk.blue('Step 2: Getting main quiz entry...'));
    const mainQuizId = '7iRj2OfUw1yYzQntjBMCJv';
    const mainQuiz = await environmentManager.getEntry(mainQuizId, 'staging');
    console.log(chalk.cyan(`Main quiz: ${mainQuiz.fields.title?.en}`));
    
    // Step 3: Collect all top-level console entry IDs
    console.log(chalk.blue('Step 3: Collecting console entry IDs...'));
    const consoleEntryIds = [
      '4ksProductQuiz_playstation5pro',
      '4ksProductQuiz_playstation5',
      '4ksProductQuiz_pc',
      '4ksProductQuiz_xboxseriesx',
      '4ksProductQuiz_xboxseriess',
      '4ksProductQuiz_nintendoswitch'
    ];
    
    // Step 4: Link console entries to main quiz
    console.log(chalk.blue('Step 4: Linking console entries to main quiz...'));
    await linkEntriesToEntry(
      mainQuizId,
      'productQuizView',
      consoleEntryIds,
      'staging',
      true // Replace existing links
    );
    
    // Step 5: Validate the structure
    console.log(chalk.blue('Step 5: Validating the complete structure...'));
    const validation = await validateLinkedEntries(
      mainQuizId,
      'productQuizView',
      'staging'
    );
    
    console.log(chalk.green(`✅ Complete workflow finished!`));
    console.log(chalk.cyan(`  - Valid console links: ${validation.valid.length}`));
    console.log(chalk.cyan(`  - Invalid console links: ${validation.invalid.length}`));
    
  } catch (error) {
    console.error(chalk.red('❌ Error in complete workflow:'), error);
  }
}

/**
 * Example 7: Cache performance demonstration
 */
async function exampleCachePerformance() {
  console.log(chalk.magenta('🎯 Example 7: Cache performance demonstration'));
  
  try {
    const entryId = '7iRj2OfUw1yYzQntjBMCJv';
    
    // First call - will cache environment
    console.log(chalk.yellow('First call (caches environment):'));
    const start1 = Date.now();
    const entry1 = await environmentManager.getEntry(entryId, 'staging');
    const duration1 = Date.now() - start1;
    console.log(chalk.green(`✅ Retrieved entry in ${duration1}ms`));
    
    // Second call - uses cached environment
    console.log(chalk.yellow('Second call (uses cached environment):'));
    const start2 = Date.now();
    const entry2 = await environmentManager.getEntry(entryId, 'staging');
    const duration2 = Date.now() - start2;
    console.log(chalk.green(`✅ Retrieved entry in ${duration2}ms`));
    
    console.log(chalk.cyan(`Performance improvement: ${Math.round((duration1 / duration2) * 100) / 100}x faster`));
    
  } catch (error) {
    console.error(chalk.red('❌ Error in cache performance test:'), error);
  }
}

/**
 * Run all examples
 */
async function runAllExamples() {
  console.log(chalk.magenta('🚀 Running all tree navigation examples...\n'));
  
  // Uncomment the examples you want to run:
  
  // await exampleCachePerformance();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // await exampleProcessSingleConsole();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // await exampleUpdateFieldValues();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // await exampleValidateLinkedEntries();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // WARNING: These examples will create/modify Contentful entries
  // Uncomment only if you want to actually create entries:
  
  // await exampleProcessAllConsoles();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // await exampleLinkEntriesToParent();
  // console.log('\n' + '='.repeat(60) + '\n');
  
  // await exampleCompleteWorkflow();
  
  console.log(chalk.magenta('🎉 All examples completed!'));
}

// Export functions for individual use
export {
  exampleProcessAllConsoles,
  exampleProcessSingleConsole,
  exampleLinkEntriesToParent,
  exampleUpdateFieldValues,
  exampleValidateLinkedEntries,
  exampleCompleteWorkflow,
  exampleCachePerformance,
  runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}
