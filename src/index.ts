import chalk from 'chalk';
import { getEntry, saveToJson } from './utils';
import { items } from './ps5Pro';

import ipad from './input_consoles/ipad.json';
import pc from './input_consoles/pc.json';
import ps4 from './input_consoles/ps4.json';
import ps4Pro from './input_consoles/ps4Pro.json';
import ps5 from './input_consoles/ps5.json';
import ps5Pro from './input_consoles/ps5Pro.json';
import sd from './input_consoles/sd.json';
import switchConsole from './input_consoles/switch.json';
import switch2 from './input_consoles/switch2.json';
import xboxOneS from './input_consoles/xboxOneS.json';
import xboxOneX from './input_consoles/xboxOneX.json';
import xboxSeriesS from './input_consoles/xboxSeriesS.json';
import xboxSeriesX from './input_consoles/xboxSeriesX.json';
import { buildTree } from './buildTree';
import fs from 'fs';
import path from 'path';

const allConsoles = [
  ipad,
  pc,
  ps4,
  ps4Pro,
  ps5,
  ps5Pro,
  sd,
  switchConsole,
  switch2,
  xboxOneS,
  xboxOneX,
  xboxSeriesS,
  xboxSeriesX,
];

function saveTreeToJson() {
  allConsoles.forEach(c => {
    const tree = buildTree(c);
    console.log(tree);
    const fileName = c[0].split('-')[0].replace("/", "-");
    const OUTPUT_CONSOLES_DIR = path.join(__dirname, '..', 'output_consoles');
    fs.existsSync(OUTPUT_CONSOLES_DIR) || fs.mkdirSync(OUTPUT_CONSOLES_DIR);
    fs.writeFileSync(
      `${OUTPUT_CONSOLES_DIR}/${fileName}.json`,
      JSON.stringify(tree, null, 2)
    );
  });
}

async function main() {
  const ENTRY_ID = '7iRj2OfUw1yYzQntjBMCJv';
  // const entry = await getEntry(ENTRY_ID);
  // console.log(chalk.blue('✨ Successfully fetched entry:'));
  // console.log(entry);

  const PC_ENTRY_ID = '14BPCQuJCZ9pC5fqhR1LZs';
  const _1440p_ENTRY_ID = '4timiKHRvbKfOyuX7IipZn';
  // await linkEntriesToEntry(PC_ENTRY_ID, 'productQuizView', [_1440p_ENTRY_ID], true);

  const tree = buildTree(items);
  // saveToJson(tree, 'tree');

  // await createTextPanelWithId();

  saveTreeToJson();
}

main();
