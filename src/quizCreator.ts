// todo

// 1. Create or Edit a contentful entry -> function
// 2. <PERSON> Entries to another entry -> function(entry, childPropertyName, children)
// 3. Log
// 4. Create a cloudinary field from url  -> function(url): Cloudinary
// 5. Walk function

import { getEntry } from './utils';
import chalk from 'chalk';

async function createEntryWithCustomId() {

  const entry = await environment.createEntryWithId('your-content-type-id', 'your-custom-entry-id', {
    fields: {
      title: {
        'en-US': 'My Custom Entry Title',
      },
      body: {
        'en-US': 'This is the body content of the entry.',
      },
    },
  });

  console.log('Entry created:', entry.sys.id);
}

async function linkEntriesToEntry(
  entryId: string,
  referenceFieldId: string,
  entriesToLinkIds: string[]
) {
  const entry = await getEntry(entryId);

  // Get current links (ensure array or initialize)
  const locale = 'en';
  const existingEntries: any[] = entry.fields[referenceFieldId][locale] || [];

  // Extract existing IDs for easy checking
  const existingIds = new Set(
    existingEntries.map((link: any) => link.sys.id)
  );

  // Filter out IDs that are already linked
  const newEntriesToLink = entriesToLinkIds.filter(
    (id) => !existingIds.has(id)
  );

  if (newEntriesToLink.length === 0) {
    console.log(
      chalk.blue(
        `No new entries to add to '${referenceFieldId}' for entry ${entryId}.`
      )
    );
    return;
  }

  // Prepare new link objects
  const entriesToLink = newEntriesToLink.map((entryToLinkId) => ({
    sys: {
      type: 'Link',
      linkType: 'Entry',
      id: entryToLinkId,
    },
  }));

  // Add new entries to the existing array
  entry.fields[referenceFieldId][locale] = [
    ...existingEntries,
    ...entriesToLink,
  ];

  // Update the entry
  const updatedEntry = await entry.update();

  console.log(
    chalk.green(
      `Added ${newEntriesToLink.length} new entries to '${referenceFieldId}' for entry ${entryId}.`
    )
  );
}
