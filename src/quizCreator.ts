// todo

// 1. Create or Edit a contentful entry -> function
// 2. Link Entries to another entry -> function(entry, childPropertyName, children)
// 3. Log
// 4. Create a cloudinary field from url  -> function(url): Cloudinary
// 5. Walk function

import { ProductQuizResult } from './buildTree';
import { getEntry } from './utils';
import { environmentManager } from './environmentManager';
import chalk from 'chalk';
import { CreateEntryProps, KeyValueMap } from 'contentful-management';

const TEXT_PANEL_ID = 'organismHeadlineMedia';
const PRODUCT_QUIZ_VIEW_ID = 'productQuizView';

async function createResultTextPanel(result: ProductQuizResult) {
  const environment = await environmentManager.getEnvironment('staging');
  const data: CreateEntryProps<KeyValueMap> = {
      fields: {
        title: {
          en: 'Script quiz test text panel 2',
        }
      },
    };
  if (result.calloutTitle) {
    data.fields = {
      ...data.fields,
      calloutTitle: {
        en: result.calloutTitle,
      },
    };
  }
  if (result.headline) {
    data.fields = {
      ...data.fields,
      headline: {
        en: result.headline,
      },
    };
  }
  if (result.disclaimerText) {
    data.fields = {
      ...data.fields,
      disclaimerText: {
          en: {
            data: {},
            content: [
              {
                nodeType: 'paragraph',
                data: {},
                content: [
                  {
                    nodeType: 'text',
                    value: result.disclaimerText,
                    marks: [],
                    data: {},
                  },
                ],
              },
            ],
            nodeType: 'document',
          },
        },
    };
  }
  const createdEntry = await environment.createEntryWithId(
    TEXT_PANEL_ID,
    result.id,
    data
  );
  console.log(
    chalk.blue('✨ Successfully created entry: ', JSON.stringify(createdEntry))
  );
  return createdEntry;
}

async function createProductQuizView(){

}

// Media mapping moved to simpleTreeProcessor.ts


// async function createEntryWithCustomId() {
//
//   const entry = await environment.createEntryWithId('your-content-type-id', 'your-custom-entry-id', {
//     fields: {
//       title: {
//         'en-US': 'My Custom Entry Title',
//       },
//       body: {
//         'en-US': 'This is the body content of the entry.',
//       },
//     },
//   });
//
//   console.log('Entry created:', entry.sys.id);
// }

export async function linkEntriesToEntry(
  entryId: string,
  referenceFieldId: string,
  entriesToLinkIds: string[],
  shouldPublish = false
) {
  const entry = await getEntry(entryId);

  // Get current links (ensure array or initialize)
  const locale = 'en';
  const existingEntries: any[] = entry.fields[referenceFieldId][locale] || [];

  // Extract existing IDs for easy checking
  const existingIds = new Set(existingEntries.map((link: any) => link.sys.id));

  // Filter out IDs that are already linked
  const newEntriesToLink = entriesToLinkIds.filter(id => !existingIds.has(id));

  if (newEntriesToLink.length === 0) {
    console.log(
      chalk.blue(
        `No new entries to add to '${referenceFieldId}' for entry ${entryId}.`
      )
    );
    return;
  }

  // Prepare new link objects
  const entriesToLink = newEntriesToLink.map(entryToLinkId => ({
    sys: {
      type: 'Link',
      linkType: 'Entry',
      id: entryToLinkId,
    },
  }));

  // Add new entries to the existing array
  entry.fields[referenceFieldId][locale] = [
    ...existingEntries,
    ...entriesToLink,
  ];

  // Update the entry
  const updatedEntry = await entry.update();
  if (shouldPublish) {
    const publishedEntry = await updatedEntry.publish();
  }

  console.log(
    chalk.green(
      `Added ${newEntriesToLink.length} new entries to '${referenceFieldId}' for entry ${entryId}.`
    )
  );
}
