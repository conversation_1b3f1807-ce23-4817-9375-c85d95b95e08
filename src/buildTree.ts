export type ProductQuizResult = {
  id: string;
  title: string;
  calloutTitle?: string;
  headline?: string;
  disclaimerText?: string;
};

export type ProductQuizNode = {
  selectorText: string;
  id: string;
  title: string;
  header?: string;
  subtitle?: string;
  result?: ProductQuizResult;
  productQuizView?: ProductQuizNode[];
};

const toHz = (str: string): string =>
  ['60', '120', '144', '240'].includes(str) ? `${str}Hz` : str;

function slugify(parts: string[]): string {
  return parts
    .map(p =>
      p
        .toLowerCase()
        .replace(/\s+/g, '')
        .replace(/[^a-z0-9]/g, '')
    )
    .join('-');
}

const deviceNames = new Set([
  'PlayStation 4',
  'PlayStation 4 Pro',
  'Nintendo Switch',
  'Nintendo Switch 2',
  'Xbox Series X',
  'Xbox Series S',
  'Xbox One X',
  'Xbox One S',
  'PC',
  'iPad/Tablet',
  'Steam Deck/Handheld',
  'PlayStation 5',
  'PlayStation 5 Pro',
]);

const resolutions = new Set(['1080p', '1440p', '4K', '8K']);

export function buildTree(items: string[]): {
  productQuizView: ProductQuizNode[];
} {
  const tree: ProductQuizNode[] = [];

  for (const item of items) {
    const [path, resultText] = item.split('_');
    const segments = path.split('-');

    const rootTitle = '4K S / Product Quiz';
    const pathParts: string[] = [];

    let currentLevel = tree;

    for (let i = 0; i < segments.length; i++) {
      const rawSegment = segments[i];
      const selectorText = toHz(rawSegment);
      pathParts.push(selectorText);

      const id = `4ksProductQuiz_${slugify(pathParts)}`;
      const title = `${rootTitle} / ${pathParts.join(' / ')}`;

      let node = currentLevel.find(n => n.selectorText === selectorText);
      if (!node) {
        node = {
          selectorText,
          id,
          title,
        };

        // Add header/subtitle if not leaf node
        if (i !== segments.length - 1) {
          if (deviceNames.has(selectorText)) {
            node.header = 'What resolution is your source set to?';
            node.subtitle = 'In other words, what do you want to capture?';
          } else if (resolutions.has(selectorText)) {
            node.header = 'What frame rate is your source set to?';
            node.subtitle =
              'You can find your console, handheld or PC’s current frame rate in the video output settings.';
          }

          node.productQuizView = [];
        }

        currentLevel.push(node);
      }

      // If this is the leaf node
      if (i === segments.length - 1) {
        const isSpecialResult = resultText
          .trim()
          .startsWith('4K S doesn’t support 4K120 capture.');

        node.result = isSpecialResult
          ? {
              id: `${id}-result`,
              title: `${title} / Result Text Panel`,
              headline: resultText.trim(),
            }
          : {
              id: `${id}-result`,
              title: `${title} / Result Text Panel`,
              calloutTitle: 'You can capture',
              headline: `Up to: ${resultText.trim()}`,
              disclaimerText:
                'Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping.',
            };
      } else {
        currentLevel = node.productQuizView!;
      }
    }
  }

  return { productQuizView: tree };
}
