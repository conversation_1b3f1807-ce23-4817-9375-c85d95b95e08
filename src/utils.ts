import { createClient, Entry } from 'contentful-management';
import config from './config';
import fs from 'fs';
import path from 'path';

export interface ExportMap {
  [id: string]: Entry;
}

export async function getEnvironment(environmentId: string) {
  const client = createClient({
    accessToken: config.CONTENTFUL_MANAGEMENT_API_KEY,
  });
  const space = await client.getSpace(config.CONTENTFUL_SPACE_ID);
  return await space.getEnvironment(environmentId);
}

const formatStringToValidFIlename = (str: string | undefined) => {
  if(!str) return '';

  return str.replace(/[ &/\\#,+()$~%.'":*?<>{}]/g, "");
};


  const environment = await getEnvironment('staging');
const OUTPUT_DIR = path.join(__dirname, '..', 'output');
export async function getEntry(id: string) {
  const entry = await environment.getEntry(id);
  const title = entry.fields.title?.en as string | undefined;

  const formattedTitle = formatStringToValidFIlename(title);
  const fileName = `${id}_${formattedTitle}`;
  saveToJson(entry, fileName);

  return entry;
}

export async function saveToJson (entry: any, fileName: string, ) {
  fs.existsSync(OUTPUT_DIR) || fs.mkdirSync(OUTPUT_DIR);
  fs.writeFileSync(`${OUTPUT_DIR}/${fileName}.json`, JSON.stringify(entry, null, 2));
}
