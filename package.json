{"name": "app", "version": "0.1.0", "description": "A node project", "main": "./dist/index.js", "scripts": {"build": "rm -rf dist && tsc", "start": "npm run build && node ./dist/index.js", "cli": "npm run build && node ./dist/cli.js", "test-cache": "npm run build && node ./dist/testCaching.js", "lint:fix": "npm run lint -- --quiet --fix", "lint": "eslint --ignore-path .gitignore --ext .ts .", "prettier": "prettier --ignore-path .gitignore --write ."}, "keywords": [], "author": "j-oliver", "license": "ISC", "devDependencies": {"@types/node": "^18.11.15", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.1", "typescript": "^4.9.4"}, "dependencies": {"chalk": "^4.1.2", "contentful": "^11.7.5", "contentful-management": "^11.54.1", "dotenv": "^17.0.1", "tiny-invariant": "^1.3.3"}}