{"productQuizView": [{"selectorText": "Nintendo Switch 2", "id": "4ksProductQuiz_nintendoswitch2", "title": "4K S / Product Quiz / Nintendo Switch 2", "header": "What resolution is your source set to?", "subtitle": "In other words, what do you want to capture?", "productQuizView": [{"selectorText": "1080p", "id": "4ksProductQuiz_nintendoswitch2-1080p", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p", "header": "What frame rate is your source set to?", "subtitle": "You can find your console, handheld or PC’s current frame rate in the video output settings.", "productQuizView": [{"selectorText": "60Hz", "id": "4ksProductQuiz_nintendoswitch2-1080p-60hz", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 60Hz", "header": "Are you playing in HDR or SDR?", "subtitle": "HDR supports billions of colors and an expanded range of colors.", "productQuizView": [{"selectorText": "SDR", "id": "4ksProductQuiz_nintendoswitch2-1080p-60hz-sdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 60Hz / SDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1080p-60hz-sdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 60Hz / SDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}, {"selectorText": "HDR", "id": "4ksProductQuiz_nintendoswitch2-1080p-60hz-hdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 60Hz / HDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1080p-60hz-hdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 60Hz / HDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}]}, {"selectorText": "120Hz", "id": "4ksProductQuiz_nintendoswitch2-1080p-120hz", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 120Hz", "header": "Are you playing in HDR or SDR?", "subtitle": "HDR supports billions of colors and an expanded range of colors.", "productQuizView": [{"selectorText": "SDR", "id": "4ksProductQuiz_nintendoswitch2-1080p-120hz-sdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 120Hz / SDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1080p-120hz-sdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 120Hz / SDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p120 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}, {"selectorText": "HDR", "id": "4ksProductQuiz_nintendoswitch2-1080p-120hz-hdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 120Hz / HDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1080p-120hz-hdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1080p / 120Hz / HDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR or 1080p120 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}]}]}, {"selectorText": "1440p", "id": "4ksProductQuiz_nintendoswitch2-1440p", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p", "header": "What frame rate is your source set to?", "subtitle": "You can find your console, handheld or PC’s current frame rate in the video output settings.", "productQuizView": [{"selectorText": "60Hz", "id": "4ksProductQuiz_nintendoswitch2-1440p-60hz", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 60Hz", "header": "Are you playing in HDR or SDR?", "subtitle": "HDR supports billions of colors and an expanded range of colors.", "productQuizView": [{"selectorText": "SDR", "id": "4ksProductQuiz_nintendoswitch2-1440p-60hz-sdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 60Hz / SDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1440p-60hz-sdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 60Hz / SDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1440p60 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}, {"selectorText": "HDR", "id": "4ksProductQuiz_nintendoswitch2-1440p-60hz-hdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 60Hz / HDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1440p-60hz-hdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 60Hz / HDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR or 1440p60 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}]}, {"selectorText": "120Hz", "id": "4ksProductQuiz_nintendoswitch2-1440p-120hz", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 120Hz", "header": "Are you playing in HDR or SDR?", "subtitle": "HDR supports billions of colors and an expanded range of colors.", "productQuizView": [{"selectorText": "SDR", "id": "4ksProductQuiz_nintendoswitch2-1440p-120hz-sdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 120Hz / SDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1440p-120hz-sdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 120Hz / SDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1440p120 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}, {"selectorText": "HDR", "id": "4ksProductQuiz_nintendoswitch2-1440p-120hz-hdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 120Hz / HDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-1440p-120hz-hdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 1440p / 120Hz / HDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR or 1440p120 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}]}]}, {"selectorText": "4K", "id": "4ksProductQuiz_nintendoswitch2-4k", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K", "header": "What frame rate is your source set to?", "subtitle": "You can find your console, handheld or PC’s current frame rate in the video output settings.", "productQuizView": [{"selectorText": "60Hz", "id": "4ksProductQuiz_nintendoswitch2-4k-60hz", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K / 60Hz", "header": "Are you playing in HDR or SDR?", "subtitle": "HDR supports billions of colors and an expanded range of colors.", "productQuizView": [{"selectorText": "SDR", "id": "4ksProductQuiz_nintendoswitch2-4k-60hz-sdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K / 60Hz / SDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-4k-60hz-sdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K / 60Hz / SDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 4K60 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}, {"selectorText": "HDR", "id": "4ksProductQuiz_nintendoswitch2-4k-60hz-hdr", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K / 60Hz / HDR", "result": {"id": "4ksProductQuiz_nintendoswitch2-4k-60hz-hdr-result", "title": "4K S / Product Quiz / Nintendo Switch 2 / 4K / 60Hz / HDR / Result Text Panel", "calloutTitle": "You can capture", "headline": "Up to: 1080p60 HDR or 4K60 SDR", "disclaimerText": "Capturing at lower resolutions and frame rates is fully supported.\nHDR input can be automatically converted to SDR using on-device tonemapping."}}]}]}]}]}